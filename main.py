"""
AI Hindi Story Audio and Video Generator
------------------------------
An autonomous Python-based agent that creates Hindi story audio and video content
based on real incidents. Generates story, audio, and video files.
"""
import re
import os
import json
import glob
import logging
import argparse
import datetime
from typing import Optional, Tuple, List, Dict

# Import dotenv
from dotenv import load_dotenv

# Import agents
from agents.writer import WriterAgent
from agents.query_generator import QueryGenerationAgent
from agents.video_metadata import TwoTierVideoMetadataAgent
from agents.story_editor import StoryEditorAgent
from agents.query_editor import QueryEditingAgent
from agents.report_reviewer import ReportReviewAgent
from agents.shorts_generator import ShortsGeneratorAgent
from agents.shorts_editing import ShortsEditingAgent
from agents.shorts_splitter import ShortsSplitterAgent
from agents.video_generator import VideoGeneratorAgent
from agents.visual_planner import VisualPlanningAgent

# Import inference modules
from inference.tts_generator import create_tts_generator
from inference.perplexity_client import PerplexityClient

# Import utilities
from utils.agent_factory import create_rate_limited_agent
from utils.shorts_utils import (get_shorts_directories, get_shorts_progress,
                                load_short_from_directory, count_completed_shorts,
                                load_segments_from_directory)

# Import schema models
from models.schema import (PerplexityReport, Story)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment(llm_provider: str = "openai",
                      dev_mode: bool = True) -> None:
    """
    Load environment variables and create necessary directories.

    Args:
        llm_provider (str): The LLM provider to use. Either "openai" or "openrouter".
        dev_mode (bool): Whether to use development mode with local TTS.
            If True, ElevenLabs API key is not required.

    Raises:
        ValueError: If an unsupported provider is specified.
        EnvironmentError: If required API keys are missing.
    """
    # Force reload environment variables
    load_dotenv(override=True)

    # Define required API keys based on provider and dev mode
    required_keys = [
        'SERPER_API_KEY',
        'PERPLEXITY_API_KEY',
        'DATAFORSEO_API_KEY',
        'DATAFORSEO_API_LOGIN'
    ]

    # Only require ElevenLabs API key if not in dev mode
    if not dev_mode:
        required_keys.append('ELEVENLABS_API_KEY')
        logger.info("Production mode: ElevenLabs API key required")
    else:
        logger.info("Development mode: ElevenLabs API key not required")

    # Add LLM provider-specific API key requirement
    if llm_provider.lower() == "openai":
        required_keys.append('OPENAI_API_KEY')
    elif llm_provider.lower() == "openrouter":
        required_keys.append('OPENROUTER_API_KEY')
    elif llm_provider.lower() == "replicate":
        required_keys.append('REPLICATE_API_KEY')
    else:
        raise ValueError(f"Unsupported LLM provider: {llm_provider}. Use 'openai', 'openrouter', or 'replicate'.")



    missing_keys = [key for key in required_keys if not os.getenv(key)]
    if missing_keys:
        raise EnvironmentError(f"Missing required API keys: {', '.join(missing_keys)}")

    # Create the main assets directory if it doesn't exist
    os.makedirs('assets', exist_ok=True)

    logger.info("Environment setup complete")

def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments for the AI Hindi Story Video Generator.

    Returns:
        argparse.Namespace: Parsed command line arguments containing all configuration options.

    Raises:
        SystemExit: If required arguments are missing or invalid arguments are provided.
    """
    parser = argparse.ArgumentParser(description='Generate AI Hindi story videos based on real incidents or fictional stories')

    # Story content arguments
    parser.add_argument('--title', help='Title of the story (required unless --continue is used)')
    parser.add_argument('--context', help='Additional context for the story (optional)')

    # Audio generation arguments
    parser.add_argument('--elevenlabs-voice-id', default='MaBqnF6LpI8cAT5sGihk',
                        help='ElevenLabs voice ID to use for narration (default: MaBqnF6LpI8cAT5sGihk)')
    parser.add_argument('--dev', action='store_true', default=True,
                        help='Use development mode with local espeak-ng TTS instead of ElevenLabs (default: True)')
    parser.add_argument('--no-dev', dest='dev', action='store_false',
                        help='Use production mode with ElevenLabs TTS instead of local espeak-ng')

    # LLM model arguments
    parser.add_argument('--model', default='gpt-4o-mini',
                        help='LLM model to use for all agents (default: gpt-4o-mini)')
    parser.add_argument('--provider', choices=['openai', 'openrouter', 'replicate'], default='openai',
                        help='LLM provider to use (default: openai)')
    parser.add_argument('--max-tokens-per-minute', dest='max_tokens_per_minute', type=int, default=30000,
                        help='Maximum tokens per minute for OpenAI API rate limiting (default: 30000)')

    # Agent-specific LLM model arguments
    parser.add_argument('--query-generator-model', dest='query_generator_model',
                        help='LLM model to use for QueryGenerationAgent (defaults to --model)')
    parser.add_argument('--query-generator-provider', dest='query_generator_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for QueryGenerationAgent (defaults to --provider)')

    parser.add_argument('--editor-model', dest='editor_model',
                        help='LLM model to use for EditorAgent (defaults to --model)')
    parser.add_argument('--editor-provider', dest='editor_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for EditorAgent (defaults to --provider)')

    parser.add_argument('--writer-model', dest='writer_model',
                        help='LLM model to use for WriterAgent (defaults to --model)')
    parser.add_argument('--writer-provider', dest='writer_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for WriterAgent (defaults to --provider)')

    parser.add_argument('--video-metadata-model', dest='video_metadata_model',
                        help='LLM model to use for TwoTierVideoMetadataAgent (defaults to --model)')
    parser.add_argument('--video-metadata-provider', dest='video_metadata_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for TwoTierVideoMetadataAgent (defaults to --provider)')

    parser.add_argument('--dialogue-splitter-model', dest='dialogue_splitter_model',
                        help='LLM model to use for DialogueSplitterAgent (defaults to --model)')
    parser.add_argument('--dialogue-splitter-provider', dest='dialogue_splitter_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for DialogueSplitterAgent (defaults to --provider)')

    parser.add_argument('--story-editor-model', dest='story_editor_model',
                        help='LLM model to use for StoryEditorAgent (defaults to --model)')
    parser.add_argument('--story-editor-provider', dest='story_editor_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for StoryEditorAgent (defaults to --provider)')

    parser.add_argument('--shorts-generator-model', dest='shorts_generator_model',
                        help='LLM model to use for ShortsGeneratorAgent (defaults to --model)')
    parser.add_argument('--shorts-generator-provider', dest='shorts_generator_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for ShortsGeneratorAgent (defaults to --provider)')

    parser.add_argument('--shorts-editing-model', dest='shorts_editing_model',
                        help='LLM model to use for ShortsEditingAgent (defaults to --model)')
    parser.add_argument('--shorts-editing-provider', dest='shorts_editing_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for ShortsEditingAgent (defaults to --provider)')

    parser.add_argument('--shorts-splitter-model', dest='shorts_splitter_model',
                        help='LLM model to use for ShortsSplitterAgent (defaults to --model)')
    parser.add_argument('--shorts-splitter-provider', dest='shorts_splitter_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for ShortsSplitterAgent (defaults to --provider)')

    # Research configuration arguments
    parser.add_argument('--reasoning-effort', choices=['low', 'medium', 'high'], default='low',
                        help='Reasoning effort level for Perplexity AI research (default: low)')

    # Debug and logging arguments
    parser.add_argument('--verbose', action='store_true', default=False,
                        help='Enable verbose output from CrewAI agents (default: False)')
    parser.add_argument('--interactive-editing', action='store_true', default=True,
                        help='Enable interactive story editing workflow (default: True)')

    # Video output options
    parser.add_argument('--video-generator-model', dest='video_generator_model',
                        help='LLM model to use for VideoGeneratorAgent (defaults to --model)')
    parser.add_argument('--video-generator-provider', dest='video_generator_provider', default='replicate',
                        choices=['replicate'],  # Only Replicate is supported for now
                        help='LLM provider to use for VideoGeneratorAgent (defaults to --provider)')
    parser.add_argument('--video-resolution', choices=['480p', '720p'], default='480p',
                        help='Video resolution for generated videos (default: 480p)')
    parser.add_argument('--visual-planner-model', dest='visual_planner_model',
                        help='LLM model to use for VisualPlanningAgent (defaults to --model)')
    parser.add_argument('--visual-planner-provider', dest='visual_planner_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for VisualPlanningAgent (defaults to --provider)')

    # Continuation options
    parser.add_argument('--continue', dest='continue_generation', nargs='?', const=True, default=False,
                        help='Continue video generation from where it left off. Can be used with a path to a specific story directory.')

    args = parser.parse_args()

    # Skip validation if using --continue flag
    if not args.continue_generation:
        # Validate that title is provided when not using --continue
        if not args.title:
            parser.error('--title is required when not using --continue')

    return args


def get_agent_config(args, agent_name: str, global_model: str, global_provider: str) -> Tuple[str, str]:
    """
    Get agent-specific model and provider configuration with fallback to global values.

    Args:
        args: Parsed command line arguments
        agent_name: Name of the agent (e.g., 'researcher', 'editor', etc.)
        global_model: Global model to use as fallback
        global_provider: Global provider to use as fallback

    Returns:
        Tuple[str, str]: (model, provider) for the specific agent
    """
    # Convert agent name to attribute format (e.g., 'researcher' -> 'researcher_model')
    model_attr = f"{agent_name}_model"
    provider_attr = f"{agent_name}_provider"

    # Get agent-specific values or fall back to global values
    agent_model = getattr(args, model_attr, None) or global_model
    agent_provider = getattr(args, provider_attr, None) or global_provider

    return agent_model, agent_provider


def get_latest_story_dir() -> Optional[str]:
    """
    Get the most recently created story directory.

    Returns:
        Optional[str]: Path to the most recent story directory, or None if no directories exist.
    """
    assets_dir = 'assets'
    if not os.path.exists(assets_dir):
        return None

    # Get all subdirectories in the assets directory
    subdirs = [d for d in os.listdir(assets_dir) if os.path.isdir(os.path.join(assets_dir, d))]
    if not subdirs:
        return None

    # Sort by creation time (newest first)
    subdirs.sort(key=lambda d: os.path.getctime(os.path.join(assets_dir, d)), reverse=True)

    # Return the newest directory
    return os.path.join(assets_dir, subdirs[0])


def get_existing_audio_files(story_dir: str) -> List[str]:
    """
    Get a list of existing audio files in the story directory.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        List[str]: List of audio file paths
    """
    audio_dir = os.path.join(story_dir, 'audio')
    if not os.path.exists(audio_dir):
        return []

    # Get all MP3 files in the audio directory
    audio_files = glob.glob(os.path.join(audio_dir, '*.mp3'))

    # Sort the files by scene and segment number
    def extract_numbers(path):
        # Extract scene and segment numbers from the filename
        match = re.search(r'scene_(\d+)_segment_(\d+)', path)
        if match:
            return (int(match.group(1)), int(match.group(2)))
        return (0, 0)

    audio_files.sort(key=extract_numbers)
    return audio_files


def get_pipeline_progress(story_dir: str) -> Dict[str, bool]:
    """
    Determine which steps of the pipeline have been completed based on existing JSON files.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        Dict[str, bool]: Dictionary indicating which steps are complete
    """
    progress = {
        'query_generated': False,
        'research_report': False,
        'story_written': False,
                'video_metadata': False,
        'audio_generated': False,
        'shorts_generated': False,
        'shorts_edited': False,
        'shorts_split': False,
        'shorts_audio_generated': False,
        'shorts_visual_planned': False,
        'shorts_video_generated': False,
        'shorts_metadata_generated': False
    }

    # Check for query generation (stored in query.md)
    query_path = os.path.join(story_dir, 'query.md')
    if os.path.exists(query_path):
        progress['query_generated'] = True

    # Check for research report (stored in report.json)
    report_path = os.path.join(story_dir, 'report.json')
    if os.path.exists(report_path):
        progress['research_report'] = True



    # Check for story
    story_path = os.path.join(story_dir, 'story.json')
    if os.path.exists(story_path):
        progress['story_written'] = True


    # Check for video metadata
    metadata_path = os.path.join(story_dir, 'video_metadata.json')
    if os.path.exists(metadata_path):
        progress['video_metadata'] = True

    # Check for audio files
    audio_dir = os.path.join(story_dir, 'audio')
    if os.path.exists(audio_dir):
        audio_files = glob.glob(os.path.join(audio_dir, '*.mp3'))
        if audio_files:
            progress['audio_generated'] = True

    # Check for shorts pipeline progress
    short_dirs = get_shorts_directories(story_dir)
    if short_dirs:
        shorts_progress = get_shorts_progress(story_dir)

        # Check if shorts are generated (at least one short.json exists)
        if any(sp['short_generated'] for sp in shorts_progress.values()):
            progress['shorts_generated'] = True

        # Check if shorts are edited (all existing shorts have been processed)
        # For now, we'll assume editing is complete if shorts are generated
        # This can be enhanced later with explicit editing tracking
        if progress['shorts_generated']:
            progress['shorts_edited'] = True

        # Check if shorts are split (at least one segments.json exists)
        if any(sp['segments_generated'] for sp in shorts_progress.values()):
            progress['shorts_split'] = True

        # Check if shorts audio is generated (at least one short has audio)
        if any(sp['audio_generated'] for sp in shorts_progress.values()):
            progress['shorts_audio_generated'] = True

        # Check if shorts visual planning is done (at least one visuals.json exists)
        if any(sp.get('visual_planned', False) for sp in shorts_progress.values()):
            progress['shorts_visual_planned'] = True

        # Check if shorts video is generated (at least one short has video)
        if any(sp['video_generated'] for sp in shorts_progress.values()):
            progress['shorts_video_generated'] = True

        # Check if shorts metadata is generated (at least one metadata.json exists)
        if any(sp['metadata_generated'] for sp in shorts_progress.values()):
            progress['shorts_metadata_generated'] = True

    return progress


def validate_story_directory(story_dir: str) -> Optional[str]:
    """
    Validate that a story directory can be used for continuation.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        Optional[str]: Validated story directory path or None if invalid
    """
    # Check if the directory exists
    if not os.path.exists(story_dir):
        logger.error(f"Directory does not exist: {story_dir}")
        return None

    # Get pipeline progress to determine if we can continue
    progress = get_pipeline_progress(story_dir)

    # Check if at least one step has been completed
    if not any(progress.values()):
        logger.error(f"No pipeline progress found in directory: {story_dir}")
        logger.error("Directory must contain at least one of: query.md, report.json, story.json, video_metadata.json, or audio files")
        return None

    # Create subdirectories if they don't exist
    os.makedirs(os.path.join(story_dir, 'audio'), exist_ok=True)

    # Log the current progress
    completed_steps = [step for step, completed in progress.items() if completed]
    logger.info(f"Found completed pipeline steps: {', '.join(completed_steps)}")

    return story_dir


def extract_title_from_existing_data(story_dir: str) -> Optional[str]:
    """
    Extract title from existing data files in the story directory.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        Optional[str]: Extracted title or None if not found
    """
    # Try to get title from story.json first
    story_path = os.path.join(story_dir, 'story.json')
    if os.path.exists(story_path):
        try:
            with open(story_path, 'r', encoding='utf-8') as f:
                story_data = json.load(f)
                return story_data.get('title')
        except Exception as e:
            logger.debug(f"Could not read title from story.json: {e}")



    # Try to extract title from directory name as last resort
    dir_name = os.path.basename(story_dir)
    # Remove timestamp suffix (format: title_YYYYMMDD_HHMMSS)
    title_match = re.match(r'^(.+)_\d{8}_\d{6}$', dir_name)
    if title_match:
        # Convert underscores back to spaces and title case
        title = title_match.group(1).replace('_', ' ').title()
        logger.debug(f"Extracted title from directory name: {title}")
        return title

    return None


def create_story_directory(title: str) -> str:
    """
    Create a unique directory for the story assets.

    Args:
        title (str): The title of the story to create a directory for.

    Returns:
        str: Path to the created story directory.
    """
    # Create a timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create a safe directory name from the title
    safe_title = re.sub(r'[^\w\s-]', '', title).strip().lower()
    safe_title = re.sub(r'[-\s]+', '_', safe_title)

    # Combine title and timestamp
    dir_name = f"{safe_title}_{timestamp}"

    # Create the full path
    story_dir = os.path.join('assets', dir_name)

    # Create the directory and subdirectories
    os.makedirs(story_dir, exist_ok=True)
    os.makedirs(os.path.join(story_dir, 'audio'), exist_ok=True)


    logger.info(f"Created story directory: {story_dir}")
    return story_dir


def main() -> Optional[str]:
    """
    Main execution function for the AI Hindi Story Audio and Video Generator.

    This function orchestrates the entire audio and video generation pipeline including:
    - Environment setup and argument parsing
    - Story research and generation
    - Audio generation
    - Video generation

    Returns:
        Optional[str]: Path to the story directory containing generated assets, or None on error.
    """
    # Parse arguments
    args = parse_arguments()

    # Extract core arguments for environment setup
    llm_provider = args.provider
    dev_mode = args.dev

    # Setup environment with all relevant providers and dev mode
    setup_environment(llm_provider=llm_provider, dev_mode=dev_mode)

    # Extract other arguments
    title = args.title
    context = args.context or ""

    elevenlabs_voice_id = args.elevenlabs_voice_id
    verbose = args.verbose
    interactive_editing = args.interactive_editing
    model = args.model
    max_tokens_per_minute = args.max_tokens_per_minute
    continue_generation = args.continue_generation
    reasoning_effort = args.reasoning_effort

    # Handle the continue flag
    story_dir = None

    if continue_generation:
        # If a path is provided with --continue
        if isinstance(continue_generation, str):
            # Use the provided path
            story_dir = continue_generation
            logger.info(f"Attempting to continue generation from specified directory: {story_dir}")

        else:
            # Find the most recent story directory
            story_dir = get_latest_story_dir()
            if story_dir:
                logger.info(f"Attempting to continue generation from most recent directory: {story_dir}")
            else:
                logger.error("No existing story directories found to continue from.")
                return None

        # Validate the story directory
        story_dir = validate_story_directory(story_dir)

        if not story_dir:
            logger.error("Cannot continue generation. Exiting.")
            return None

        # Log that we're continuing from an existing directory
        logger.info(f"Continuing video generation from: {story_dir}")

        # If title is not provided when continuing, try to extract it from existing data
        if not title:
            title = extract_title_from_existing_data(story_dir)
            if not title:
                logger.error("Could not determine title from existing data and no title provided")
                return None
            logger.info(f"Extracted title from existing data: '{title}'")

    else:
        # Log normal generation parameters
        logger.info(f"Starting real story generation for title: '{title}'")


        logger.info(f"TTS Provider: {'Coqui-AI (local)' if args.dev else f'ElevenLabs (voice ID: {elevenlabs_voice_id})'}")
        logger.info(f"Using LLM provider: {llm_provider}")
        logger.info(f"Using LLM model: {model}")
        logger.info(f"CrewAI verbose mode: {'Enabled' if verbose else 'Disabled'}")
        logger.info(f"Interactive editing mode: {'Enabled' if interactive_editing else 'Disabled'}")

        # Create a new directory for this story
        story_dir = create_story_directory(title)

    # Get pipeline progress to determine what steps to skip
    progress = get_pipeline_progress(story_dir)

    # Initialize variables for pipeline data
    story: Optional[Story] = None
    perplexity_report: Optional[PerplexityReport] = None

    # Define paths for all assets
    story_json_path = os.path.join(story_dir, 'story.json')
    query_md_path = os.path.join(story_dir, 'query.md')
    report_json_path = os.path.join(story_dir, 'report.json')

    # Load existing data if available
    if progress['story_written']:
        logger.info("Loading existing story data...")
        try:
            with open(story_json_path, 'r', encoding='utf-8') as f:
                story_data = json.load(f)
                story = Story(**story_data)
            logger.info(f"Loaded existing story with {len(story.scenes)} scenes")
        except Exception as e:
            logger.error(f"Error reading existing story: {str(e)}")
            progress['story_written'] = False

    if progress['research_report']:
        logger.info("Loading existing research report...")
        try:
            with open(report_json_path, 'r', encoding='utf-8') as f:
                report_data = json.load(f)
                perplexity_report = PerplexityReport(**report_data)
            logger.info("Loaded existing research report")
        except Exception as e:
            logger.error(f"Error reading existing research report: {str(e)}")
            progress['research_report'] = False



    # Step 1: Generate research query (if not already done)
    research_query = None
    if not progress['query_generated']:
        logger.info("Step 1: Generating research query")

        # Get agent-specific configuration
        query_generator_model, query_generator_provider = get_agent_config(args, 'query_generator', model, llm_provider)
        logger.info(f"QueryGenerationAgent using model: {query_generator_model}, provider: {query_generator_provider}")

        query_generator = QueryGenerationAgent(
            verbose=verbose,
            model=query_generator_model,
            provider=query_generator_provider
        )

        research_query = query_generator.generate_research_query(title, context)
        logger.info("Research query generated successfully")

        # Save initial query to markdown file (before editing)
        query_generator.save_query_to_file(query_md_path, research_query, title, context, is_edited=False)
        logger.info(f"Initial research query saved to: {query_md_path}")

        # Interactive query editing (if enabled)
        if interactive_editing:
            logger.info("Starting interactive query editing workflow")

            query_editor = QueryEditingAgent(
                verbose=verbose,
                model=query_generator_model,
                provider=query_generator_provider
            )

            research_query = query_editor.interactive_edit(
                research_query,
                title,
                context,
                query_file_path=query_md_path
            )

            logger.info("Query editing complete")

        progress['query_generated'] = True
    else:
        logger.info("Step 1: Skipping query generation (already completed)")
        # Load existing query from file
        try:
            with open(query_md_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # Try to extract edited query first, then fall back to original
                edited_query_start = content.find("**Generated Query (Edited):**")
                if edited_query_start != -1:
                    research_query = content[edited_query_start + len("**Generated Query (Edited):**"):].strip()
                    logger.info("Loaded edited research query from file")
                else:
                    # Extract original query from markdown
                    query_start = content.find("**Generated Query:**")
                    if query_start != -1:
                        research_query = content[query_start + len("**Generated Query:**"):].strip()
                        logger.info("Loaded original research query from file")
                    else:
                        # Fallback: use entire content if format is different
                        research_query = content.strip()
                        logger.info("Loaded research query from file (fallback format)")

                # Remove any leading newlines
                research_query = research_query.lstrip('\n')
        except Exception as e:
            logger.error(f"Error loading query from {query_md_path}: {e}")
            return None

        # Offer interactive query editing on resume if enabled
        if interactive_editing and research_query:
            logger.info("Resumed run: offering interactive query editing before research")
            query_generator_model, query_generator_provider = get_agent_config(args, 'query_generator', model, llm_provider)
            query_editor = QueryEditingAgent(
                verbose=verbose,
                model=query_generator_model,
                provider=query_generator_provider
            )
            research_query = query_editor.interactive_edit(
                research_query,
                title,
                context,
                query_file_path=query_md_path
            )

    # Step 2: Generate research report using Perplexity AI Sonar (if not already done)
    if not progress['research_report']:
        logger.info("Step 2: Generating research report using Perplexity AI Sonar")

        if not research_query:
            logger.error("Research query is required but not available")
            return None

        # Generate research report using Perplexity AI Sonar
        perplexity_client = PerplexityClient()
        perplexity_response = perplexity_client.generate_research_report(research_query, reasoning_effort=reasoning_effort)
        report_content = perplexity_client.extract_report_content(perplexity_response)

        # Create PerplexityReport object
        perplexity_report = PerplexityReport(
            query=research_query,
            report_content=report_content,
            citations=perplexity_response.get("citations", []),
            usage_stats=perplexity_response.get("usage", {})
        )

        # Save PerplexityReport to JSON
        perplexity_client.save_report(perplexity_report.model_dump(), report_json_path)
        logger.info(f"PerplexityReport saved to {report_json_path}")

        # Interactive in-depth report review (if enabled)
        if interactive_editing:
            logger.info("Starting interactive in-depth report review")

            report_reviewer = ReportReviewAgent()
            should_continue = report_reviewer.interactive_review(perplexity_report, title)

            if not should_continue:
                logger.info("Pipeline stopped for manual report editing")
                print("\nPipeline stopped. You can manually edit the report.json file and use --continue to resume.")
                print("Note: The research query is available in query.md for reference.")
                return None

            logger.info("In-depth report review complete, continuing with pipeline")

        progress['research_report'] = True

    else:
        logger.info("Step 2: Skipping research report generation (already completed)")

    # Step 3: Create documentary narration using WriterAgent (if not already done)
    if not progress['story_written']:
        logger.info("Step 3: Creating Hindi documentary-style narration")

        # Get agent-specific configuration
        writer_model, writer_provider = get_agent_config(args, 'writer', model, llm_provider)
        logger.info(f"WriterAgent using model: {writer_model}, provider: {writer_provider}")

        writer_agent = WriterAgent(verbose=verbose, model=writer_model, provider=writer_provider)
        story = writer_agent.write_documentary_story(perplexity_report, title)

        # Save story to JSON
        with open(story_json_path, 'w', encoding='utf-8') as f:
            json.dump(story.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"Documentary story written and saved to {story_json_path}")
        progress['story_written'] = True
    else:
        logger.info("Step 3: Skipping story writing (already completed)")

    # Step 4: Interactive story editing (if enabled)
    if interactive_editing:
        logger.info("Starting interactive story editing workflow")

        # Get agent-specific configuration
        story_editor_model, story_editor_provider = get_agent_config(args, 'story_editor', model, llm_provider)
        logger.info(f"StoryEditorAgent using model: {story_editor_model}, provider: {story_editor_provider}")

        editor = create_rate_limited_agent(
            role="Story Editor",
            goal="Edit and refine the story based on user feedback",
            backstory="You are an expert story editor who helps refine and improve stories.",
            model=story_editor_model,
            max_tokens_per_minute=max_tokens_per_minute,
            verbose=verbose,
            provider=story_editor_provider
        )

        story_editor = StoryEditorAgent(verbose=verbose, model=story_editor_model, provider=story_editor_provider)
        story_editor.llm = editor.llm  # Use the rate-limited LLM

        # Pass the story_json_path to enable real-time updates during editing
        story = story_editor.interactive_edit(story, perplexity_report, story_json_path)

        # Final save is still needed to ensure consistency
        with open(story_json_path, 'w', encoding='utf-8') as f:
            json.dump(story.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"Edited story saved to {story_json_path}")
        print("Interactive editing complete. Proceeding to shorts generation...")

    # Step 5: Generate shorts from the complete story (if not already done)
    if not progress['shorts_generated']:
        logger.info("Step 5: Generating shorts from the complete story")

        # Get agent-specific configuration
        shorts_generator_model, shorts_generator_provider = get_agent_config(args, 'shorts_generator', model, llm_provider)
        logger.info(f"ShortsGeneratorAgent using model: {shorts_generator_model}, provider: {shorts_generator_provider}")

        shorts_generator = ShortsGeneratorAgent(verbose=verbose,
                                                model=shorts_generator_model,
                                                provider=shorts_generator_provider)
        shorts = shorts_generator.generate_shorts(story)

        # Save shorts to individual directories
        shorts_generator.save_shorts_to_directories(shorts, story_dir)

        logger.info(f"Generated {len(shorts)} shorts and saved to individual directories")
        progress['shorts_generated'] = True
    else:
        logger.info("Step 5: Skipping shorts generation (already completed)")

    # Step 6: Interactive shorts editing (if enabled)
    if interactive_editing and progress['shorts_generated']:
        logger.info("Step 6: Starting interactive shorts editing workflow")

        # Get agent-specific configuration
        shorts_editing_model, shorts_editing_provider = get_agent_config(args, 'shorts_editing', model, llm_provider)
        logger.info(f"ShortsEditingAgent using model: {shorts_editing_model}, provider: {shorts_editing_provider}")

        shorts_editing_agent = ShortsEditingAgent(verbose=verbose, model=shorts_editing_model, provider=shorts_editing_provider)

        # Process each short individually
        short_dirs = get_shorts_directories(story_dir)

        for short_dir in short_dirs:
            short = load_short_from_directory(short_dir)
            if short is None:
                logger.warning(f"Could not load short from {short_dir}, skipping")
                continue

            short_json_path = os.path.join(short_dir, "short.json")
            edited_short = shorts_editing_agent.interactive_edit(short, perplexity_report, short_json_path)

            # Final save to ensure consistency
            shorts_editing_agent.save_short_to_json(edited_short, short_json_path)

        logger.info("Interactive shorts editing complete")
        progress['shorts_edited'] = True
    else:
        if not interactive_editing:
            logger.info("Step 6: Skipping shorts editing (interactive editing disabled)")
            progress['shorts_edited'] = True
        else:
            logger.info("Step 6: Skipping shorts editing (shorts not generated yet)")

    # Step 7: Split shorts into segments for text-to-video (if not already done)
    if not progress['shorts_split'] and progress['shorts_edited']:
        logger.info("Step 7: Splitting shorts into segments for text-to-video generation")

        # Get agent-specific configuration
        shorts_splitter_model, shorts_splitter_provider = get_agent_config(args, 'shorts_splitter', model, llm_provider)
        logger.info(f"ShortsSplitterAgent using model: {shorts_splitter_model}, provider: {shorts_splitter_provider}")

        shorts_splitter = ShortsSplitterAgent(verbose=verbose,
                                              model=shorts_splitter_model,
                                              provider=shorts_splitter_provider)
        shorts_splitter.process_all_shorts(story_dir)

        logger.info("Shorts splitting completed")
        progress['shorts_split'] = True
    else:
        if not progress['shorts_edited']:
            logger.info("Step 7: Skipping shorts splitting (shorts not edited yet)")
        else:
            logger.info("Step 7: Skipping shorts splitting (already completed)")

    # Step 8: Generate audio for shorts segments (if not already done)
    if not progress['shorts_audio_generated'] and progress['shorts_split']:
        logger.info("Step 8: Generating audio for shorts segments")

        # Use the same TTS generator as for regular audio
        dev_mode = args.dev
        tts_generator = create_tts_generator(
            dev_mode=dev_mode,
            voice_id=elevenlabs_voice_id
        )

        short_dirs = get_shorts_directories(story_dir)

        for short_dir in short_dirs:
            segments = load_segments_from_directory(short_dir)
            if segments is None:
                logger.warning(f"Could not load segments from {short_dir}, skipping")
                continue

            # Check if audio already exists for this short
            audio_dir = os.path.join(short_dir, 'audio')
            existing_audio = []
            if os.path.exists(audio_dir):
                existing_audio = [f for f in os.listdir(audio_dir) if f.endswith(('.mp3', '.wav'))]

            if len(existing_audio) >= len(segments):
                logger.info(f"Audio already exists for short {os.path.basename(short_dir)}, skipping")
                continue

            logger.info(f"Generating audio for short {os.path.basename(short_dir)} ({len(segments)} segments)")

            # Generate audio for this short's segments
            short_audio_paths = tts_generator.generate_audio(segments, audio_dir)
            logger.info(f"Generated {len(short_audio_paths)} audio files for short {os.path.basename(short_dir)}")

        progress['shorts_audio_generated'] = True
        logger.info("Shorts audio generation completed")

    else:
        if not progress['shorts_split']:
            logger.info("Step 8: Skipping shorts audio generation (shorts not split yet)")
        else:
            logger.info("Step 8: Skipping shorts audio generation (already completed)")

    # Step 9: Create visual plans for shorts segments (if not already done)
    if not progress['shorts_visual_planned'] and progress['shorts_audio_generated']:
        logger.info("Step 9: Creating visual plans for shorts segments")

        # Get agent-specific configuration
        visual_planner_model, visual_planner_provider = get_agent_config(args, 'visual_planner', model, llm_provider)
        logger.info(f"VisualPlanningAgent using model: {visual_planner_model}, provider: {visual_planner_provider}")

        visual_planner = VisualPlanningAgent(
            verbose=verbose,
            model=visual_planner_model,
            provider=visual_planner_provider
        )

        # Process all shorts to create visual plans
        if visual_planner.process_all_shorts(story_dir):
            progress['shorts_visual_planned'] = True
            logger.info("Visual planning completed for all shorts")
        else:
            logger.error("Visual planning failed for some shorts")

    else:
        if not progress['shorts_audio_generated']:
            logger.info("Step 9: Skipping visual planning (audio not generated yet)")
        else:
            logger.info("Step 9: Skipping visual planning (already completed)")

    # Step 10: Generate videos for shorts segments (if not already done)
    if not progress['shorts_video_generated'] and progress['shorts_visual_planned']:
        logger.info("Step 10: Generating videos for shorts segments")

        # Get agent-specific configuration
        video_generator_model, video_generator_provider = get_agent_config(args, 'video_generator', model, llm_provider)
        logger.info(f"VideoGeneratorAgent using model: {video_generator_model}, provider: {video_generator_provider}")

        # Create the video generator agent
        video_generator = VideoGeneratorAgent(
            verbose=verbose,
            model=video_generator_model,
            provider=video_generator_provider,
            video_resolution=args.video_resolution
        )

        # Process all shorts to generate videos
        all_generated_videos = video_generator.process_all_shorts(story_dir)

        # Log results
        total_videos = sum(len(videos) for videos in all_generated_videos.values())
        logger.info(f"Generated {total_videos} videos across {len(all_generated_videos)} shorts")

        progress['shorts_video_generated'] = True
        logger.info("Shorts video generation completed")

    else:
        if not progress['shorts_visual_planned']:
            logger.info("Step 10: Skipping shorts video generation (visual planning not completed yet)")
        else:
            logger.info("Step 10: Skipping shorts video generation (already completed)")

    # Step 11: Generate two-tier video metadata (if not already done)
    video_metadata_json_path = os.path.join(story_dir, 'video_metadata.json')

    if not progress['video_metadata'] and progress['shorts_generated']:
        logger.info("Step 11: Generating two-tier video metadata (global + short-specific)")

        # Get agent-specific configuration
        video_metadata_model, video_metadata_provider = get_agent_config(args, 'video_metadata', model, llm_provider)
        logger.info(f"TwoTierVideoMetadataAgent using model: {video_metadata_model}, provider: {video_metadata_provider}")

        try:
            # Load all shorts from directories
            short_dirs = get_shorts_directories(story_dir)
            shorts = []

            for short_dir in short_dirs:
                short = load_short_from_directory(short_dir)
                if short is not None:
                    shorts.append(short)

            if not shorts:
                logger.warning("No shorts found, skipping two-tier metadata generation")

            else:
                # Initialize the two-tier metadata agent
                two_tier_agent = TwoTierVideoMetadataAgent(
                    verbose=verbose,
                    model=video_metadata_model,
                    provider=video_metadata_provider
                )

                # Generate both global and short-specific metadata
                global_metadata, short_metadata_list = two_tier_agent.generate_two_tier_metadata(story, shorts, story_dir)

                logger.info(f"Global metadata saved to {video_metadata_json_path}")
                logger.info(f"Generated base title: {global_metadata.base_title}")
                logger.info(f"Generated {len(global_metadata.base_hashtags)} base hashtags")
                logger.info(f"Generated short-specific metadata for {len(short_metadata_list)} shorts")

                progress['video_metadata'] = True
                progress['shorts_metadata_generated'] = True

        except Exception as e:
            logger.warning(f"Two-tier video metadata generation failed: {str(e)}")
            logger.warning("Continuing without video metadata...")

    elif not progress['shorts_generated']:
        logger.info("Step 11: Skipping two-tier metadata generation (shorts not generated yet)")

    else:
        logger.info("Step 11: Skipping two-tier metadata generation (already completed)")

    return story_dir

if __name__ == "__main__":
    try:
        story_dir = main()

        if story_dir:
            print(f"\nSuccess! Real story audio and video generation pipeline completed.")
            print(f"Story directory: {story_dir}")
            print(f"Generated assets:")
            print(f"  - Research query: {os.path.join(story_dir, 'query.md')}")
            print(f"  - Research report: {os.path.join(story_dir, 'report.json')}")

            print(f"  - Story data: {os.path.join(story_dir, 'story.json')}")
            print(f"  - Video metadata: {os.path.join(story_dir, 'video_metadata.json')}")
            print(f"  - Audio files: {os.path.join(story_dir, 'audio/')}")

            # Show shorts information
            short_dirs = get_shorts_directories(story_dir)
            completed_shorts, total_shorts = count_completed_shorts(story_dir)
            print(f"  - Shorts generated: {total_shorts} shorts in individual directories")
            print(f"  - Shorts completed: {completed_shorts}/{total_shorts}")

            for short_dir in short_dirs:
                short_name = os.path.basename(short_dir)
                print(f"    * {short_name}/: short.json, segments.json, audio/, video/, metadata.json")

        else:
            print("\nError: Real story audio and video generation pipeline failed.")

    except Exception as e:
        logger.error(f"Error during execution: {str(e)}", exc_info=True)
        print(f"\nError: {str(e)}")
