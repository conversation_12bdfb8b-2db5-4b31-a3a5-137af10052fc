"""
Image Generator
---------------
Generates start and end frame images using google/imagen-4 via Replicate API.
Used for handling text, logos, and complex environments in video generation.
"""

import os
import logging
import requests
from typing import Optional

import replicate

logger = logging.getLogger(__name__)


class ImageGenerator:
    """
    Image generator that uses google/imagen-4 via Replicate API for generating
    start and end frame images to address video generation model limitations.
    """

    def __init__(self, replicate_api_key: Optional[str] = None):
        """
        Initialize the image generator.

        Args:
            replicate_api_key (str, optional): Replicate API key. If not provided,
                will use REPLICATE_API_TOKEN environment variable.
        """
        self.replicate_api_key = replicate_api_key or os.getenv('REPLICATE_API_TOKEN')
        
        if not self.replicate_api_key:
            raise ValueError("Replicate API key is required. Set REPLICATE_API_TOKEN environment variable or pass replicate_api_key parameter.")

        # Configure replicate client
        os.environ['REPLICATE_API_TOKEN'] = self.replicate_api_key

    def generate_image(self, prompt: str, output_path: str, aspect_ratio: str = "9:16") -> bool:
        """
        Generate an image using google/imagen-4 model.

        Args:
            prompt (str): Text prompt for image generation
            output_path (str): Path to save the generated image
            aspect_ratio (str): Aspect ratio for the image (default: "9:16" for vertical videos)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Generating image with prompt: {prompt[:100]}...")
            logger.debug(f"Output path: {output_path}")

            # Generate image using google/imagen-4
            image_url = replicate.run(
                "google/imagen-4",
                input={
                    "prompt": prompt,
                    "aspect_ratio": aspect_ratio,
                    "output_format": "png",
                    "output_quality": 90
                }
            )

            # Download the image
            response = requests.get(image_url, timeout=60)
            response.raise_for_status()

            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Save the image
            with open(output_path, 'wb') as f:
                f.write(response.content)

            logger.info(f"Image saved successfully to {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error generating image: {str(e)}")
            return False

    def generate_start_image(self, visual_segment, short_dir: str) -> Optional[str]:
        """
        Generate start image for a visual segment.

        Args:
            visual_segment: VisualSegment object with start_image_prompt
            short_dir (str): Path to the short directory

        Returns:
            Optional[str]: Path to the generated start image, or None if failed
        """
        if not visual_segment.start_with_image or not visual_segment.start_image_prompt:
            return None

        # Create images directory if it doesn't exist
        images_dir = os.path.join(short_dir, 'images')
        os.makedirs(images_dir, exist_ok=True)

        # Generate filename for start image
        start_image_path = os.path.join(images_dir, f"segment_{visual_segment.segment_number}_start.png")

        # Skip if image already exists
        if os.path.exists(start_image_path):
            logger.info(f"Start image already exists, skipping: {start_image_path}")
            return start_image_path

        # Generate the image
        if self.generate_image(visual_segment.start_image_prompt, start_image_path):
            return start_image_path
        else:
            return None

    def generate_end_image(self, visual_segment, short_dir: str) -> Optional[str]:
        """
        Generate end image for a visual segment.

        Args:
            visual_segment: VisualSegment object with end_image_prompt
            short_dir (str): Path to the short directory

        Returns:
            Optional[str]: Path to the generated end image, or None if failed
        """
        if not visual_segment.end_with_image or not visual_segment.end_image_prompt:
            return None

        # Create images directory if it doesn't exist
        images_dir = os.path.join(short_dir, 'images')
        os.makedirs(images_dir, exist_ok=True)

        # Generate filename for end image
        end_image_path = os.path.join(images_dir, f"segment_{visual_segment.segment_number}_end.png")

        # Skip if image already exists
        if os.path.exists(end_image_path):
            logger.info(f"End image already exists, skipping: {end_image_path}")
            return end_image_path

        # Generate the image
        if self.generate_image(visual_segment.end_image_prompt, end_image_path):
            return end_image_path
        else:
            return None

    def get_previous_end_frame(self, current_segment_number: int, short_dir: str) -> Optional[str]:
        """
        Get the end frame from the previous segment for continuation.

        Args:
            current_segment_number (int): Current segment number
            short_dir (str): Path to the short directory

        Returns:
            Optional[str]: Path to the previous segment's end image, or None if not found
        """
        if current_segment_number <= 1:
            return None

        previous_segment_number = current_segment_number - 1
        images_dir = os.path.join(short_dir, 'images')
        previous_end_image = os.path.join(images_dir, f"segment_{previous_segment_number}_end.png")

        if os.path.exists(previous_end_image):
            return previous_end_image
        else:
            logger.warning(f"Previous end frame not found: {previous_end_image}")
            return None
