"""
Video Combiner
--------------
Combines audio and video files for each short to create final output videos.
"""

import os
import logging
from typing import List, Optional

logger = logging.getLogger(__name__)


class VideoCombiner:
    """
    Combines audio and video files for shorts to create final output videos.
    """

    def __init__(self):
        """Initialize the video combiner."""
        pass

    def combine_short_audio_video(self, short_dir: str) -> Optional[str]:
        """
        Combine audio and video files for a single short.

        Args:
            short_dir (str): Path to the short directory

        Returns:
            Optional[str]: Path to the combined video file, or None if failed
        """
        short_name = os.path.basename(short_dir)
        
        try:
            # Find audio file
            audio_file = self._find_audio_file(short_dir)
            if not audio_file:
                logger.error(f"No audio file found for {short_name}")
                return None

            # Find video files
            video_files = self._find_video_files(short_dir)
            if not video_files:
                logger.error(f"No video files found for {short_name}")
                return None

            logger.info(f"Combining audio and video for {short_name}")
            logger.info(f"Audio file: {audio_file}")
            logger.info(f"Video files: {len(video_files)} segments")

            # Create output directory
            output_dir = os.path.join(short_dir, 'final')
            os.makedirs(output_dir, exist_ok=True)

            # Output file path
            output_file = os.path.join(output_dir, f"{short_name}_final.mp4")

            # Skip if final video already exists
            if os.path.exists(output_file):
                logger.info(f"Final video already exists, skipping: {output_file}")
                return output_file

            # Combine video segments first
            combined_video_path = self._combine_video_segments(video_files, short_dir)
            if not combined_video_path:
                logger.error(f"Failed to combine video segments for {short_name}")
                return None

            # Add audio to the combined video
            final_video_path = self._add_audio_to_video(combined_video_path, audio_file, output_file)
            
            # Clean up temporary combined video
            if combined_video_path != final_video_path and os.path.exists(combined_video_path):
                os.remove(combined_video_path)

            if final_video_path:
                logger.info(f"Successfully created final video: {final_video_path}")
                return final_video_path
            else:
                logger.error(f"Failed to add audio to video for {short_name}")
                return None

        except Exception as e:
            logger.error(f"Error combining audio and video for {short_name}: {str(e)}")
            return None

    def _find_audio_file(self, short_dir: str) -> Optional[str]:
        """Find the audio file for the short."""
        audio_dir = os.path.join(short_dir, 'audio')
        if not os.path.exists(audio_dir):
            return None

        # Look for complete audio file
        for file in os.listdir(audio_dir):
            if file.endswith(('.mp3', '.wav')) and 'complete' in file:
                return os.path.join(audio_dir, file)

        return None

    def _find_video_files(self, short_dir: str) -> List[str]:
        """Find and sort video files for the short."""
        video_dir = os.path.join(short_dir, 'video')
        if not os.path.exists(video_dir):
            return []

        video_files = []
        for file in os.listdir(video_dir):
            if file.endswith('.mp4') and 'segment_' in file:
                video_files.append(os.path.join(video_dir, file))

        # Sort by segment number
        video_files.sort(key=lambda x: int(x.split('segment_')[-1].split('.')[0]))
        return video_files

    def _combine_video_segments(self, video_files: List[str], short_dir: str) -> Optional[str]:
        """Combine multiple video segments into a single video."""
        try:
            from moviepy.editor import VideoFileClip, concatenate_videoclips

            # Load all video clips
            clips = []
            for video_file in video_files:
                clip = VideoFileClip(video_file)
                clips.append(clip)

            # Concatenate all clips
            final_clip = concatenate_videoclips(clips, method="compose")

            # Save combined video
            temp_video_path = os.path.join(short_dir, 'temp_combined_video.mp4')
            final_clip.write_videofile(
                temp_video_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # Clean up clips
            for clip in clips:
                clip.close()
            final_clip.close()

            return temp_video_path

        except Exception as e:
            logger.error(f"Error combining video segments: {str(e)}")
            return None

    def _add_audio_to_video(self, video_path: str, audio_path: str, output_path: str) -> Optional[str]:
        """Add audio to video, handling length mismatches."""
        try:
            from moviepy.editor import VideoFileClip, AudioFileClip

            # Load video and audio
            video_clip = VideoFileClip(video_path)
            audio_clip = AudioFileClip(audio_path)

            logger.info(f"Video duration: {video_clip.duration:.2f}s, Audio duration: {audio_clip.duration:.2f}s")

            # Handle length mismatches
            if video_clip.duration > audio_clip.duration:
                # Video is longer - loop audio or extend with silence
                logger.info("Video is longer than audio, extending audio")
                # For now, just use the audio as-is and let video continue without audio
                final_clip = video_clip.set_audio(audio_clip)
            elif audio_clip.duration > video_clip.duration:
                # Audio is longer - trim audio to match video
                logger.info("Audio is longer than video, trimming audio")
                audio_clip = audio_clip.subclip(0, video_clip.duration)
                final_clip = video_clip.set_audio(audio_clip)
            else:
                # Lengths match
                final_clip = video_clip.set_audio(audio_clip)

            # Write final video
            final_clip.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                verbose=False,
                logger=None
            )

            # Clean up
            video_clip.close()
            audio_clip.close()
            final_clip.close()

            return output_path

        except Exception as e:
            logger.error(f"Error adding audio to video: {str(e)}")
            return None

    def combine_all_shorts(self, story_dir: str) -> List[str]:
        """
        Combine audio and video for all shorts in the story directory.

        Args:
            story_dir (str): Path to the story directory

        Returns:
            List[str]: List of paths to final combined videos
        """
        from utils.shorts_utils import get_shorts_directories

        short_dirs = get_shorts_directories(story_dir)
        if not short_dirs:
            logger.warning(f"No shorts directories found in {story_dir}")
            return []

        final_videos = []

        for short_dir in short_dirs:
            short_name = os.path.basename(short_dir)
            logger.info(f"Processing final video combination for {short_name}")

            try:
                final_video_path = self.combine_short_audio_video(short_dir)
                if final_video_path:
                    final_videos.append(final_video_path)
                    logger.info(f"Successfully combined {short_name}")
                else:
                    logger.error(f"Failed to combine {short_name}")

            except Exception as e:
                logger.error(f"Error processing {short_name}: {str(e)}")

        logger.info(f"Final video combination completed. Created {len(final_videos)} videos out of {len(short_dirs)} shorts")
        return final_videos
