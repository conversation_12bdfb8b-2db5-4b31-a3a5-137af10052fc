"""
Output Parsers
-------------
Langchain output parsers for structured data in the story generation pipeline.
"""

import re
import json
import logging
from typing import Type, List, Optional

from pydantic import BaseModel
from langchain.prompts import PromptTemplate
from langchain.output_parsers import PydanticOutputParser

from models.schema import (
    Story,
    Scene,
    ResearchData,
    EnhancedResearchData,
    Short,
    ShortsList,
    ShortSegment,
    ShortSegmentList,
    GlobalMetadata,
    ShortSpecificMetadata,
    VisualPlan,
    VisualSegment,
)

logger = logging.getLogger(__name__)


class OutputParser:
    """Base class for output parsers."""

    @staticmethod
    def create_parser(model_class: Type[BaseModel]) -> PydanticOutputParser:
        """Create a Pydantic output parser for the given model class."""
        return PydanticOutputParser(pydantic_object=model_class)

    @staticmethod
    def get_format_instructions(parser: PydanticOutputParser) -> str:
        """Get format instructions for the given parser."""
        return parser.get_format_instructions()

    @staticmethod
    def add_format_instructions_to_prompt(prompt: str, parser: PydanticOutputParser) -> str:
        """Add format instructions to a prompt."""
        format_instructions = parser.get_format_instructions()
        return f"{prompt}\n\n{format_instructions}"

    @staticmethod
    def create_prompt_template(template: str, parser: PydanticOutputParser) -> PromptTemplate:
        """Create a prompt template with format instructions."""
        format_instructions = parser.get_format_instructions()
        template_with_instructions = f"{template}\n\n{format_instructions}"

        # Extract input variables from the template
        input_variables = [
            var.strip('{}') for var in re.findall(r'\{[^{}]+\}', template)
        ]

        return PromptTemplate(
            template=template_with_instructions,
            input_variables=input_variables,
            partial_variables={"format_instructions": format_instructions}
        )

    @staticmethod
    def parse_output(parser: PydanticOutputParser, output: str) -> Optional[BaseModel]:
        """Parse the output using the given parser."""
        try:
            # First try to parse directly
            return parser.parse(output)
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                # For list output
                if parser.pydantic_object.__name__ == "List":
                    json_match = re.search(r'(\[[\s\S]*\])', output)
                    if json_match:
                        json_str = json_match.group(1)
                        data = json.loads(json_str)
                        return parser.parse(json.dumps(data))
                # For object output
                else:
                    json_match = re.search(r'({[\s\S]*})', output)
                    if json_match:
                        json_str = json_match.group(1)
                        data = json.loads(json_str)
                        return parser.parse(json.dumps(data))
            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None


class ResearchDataParser(OutputParser):
    """Parser for research data."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for ResearchData."""
        return OutputParser.create_parser(ResearchData)


class EnhancedResearchDataParser(OutputParser):
    """Parser for enhanced research data."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for EnhancedResearchData."""
        return OutputParser.create_parser(EnhancedResearchData)


class StoryParser(OutputParser):
    """Parser for story data."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for Story."""
        return OutputParser.create_parser(Story)

    @staticmethod
    def create_scene_parser() -> PydanticOutputParser:
        """Create a parser for Scene."""
        return OutputParser.create_parser(Scene)

    @staticmethod
    def parse_scene_output(parser, output: str) -> Optional[Scene]:
        """
        Parse the output using the given parser with enhanced error handling for Scene objects.
        """
        try:
            # First try to parse directly
            return parser.parse(output)
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                json_match = re.search(r'({[\s\S]*})', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Manually create a Scene object
                    try:
                        scene = Scene(
                            scene_number=data.get("scene_number", 1),
                            narration=data.get("narration", "")
                        )
                        return scene
                    except Exception as scene_e:
                        logger.warning(f"Error creating scene: {str(scene_e)}")
            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None







class GlobalMetadataParser(OutputParser):
    """Parser for global metadata."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for GlobalMetadata."""
        return OutputParser.create_parser(GlobalMetadata)


class ShortSpecificMetadataParser(OutputParser):
    """Parser for short-specific metadata."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for ShortSpecificMetadata."""
        return OutputParser.create_parser(ShortSpecificMetadata)


class ResearchQueryParser(OutputParser):
    """Parser for research queries (simple string output)."""

    @staticmethod
    def parse_query_output(output: str) -> str:
        """
        Extract the research query from agent output.

        Args:
            output (str): Raw output from the query generation agent

        Returns:
            str: Cleaned research query
        """
        # Remove any markdown formatting or extra text
        query = output.strip()

        # Remove common prefixes that agents might add
        prefixes_to_remove = [
            "Research Query:",
            "Query:",
            "Detailed Research Query:",
            "Here is the research query:",
            "The research query is:",
        ]

        for prefix in prefixes_to_remove:
            if query.startswith(prefix):
                query = query[len(prefix):].strip()

        # Remove markdown code blocks if present
        if query.startswith("```") and query.endswith("```"):
            lines = query.split('\n')
            if len(lines) > 2:
                query = '\n'.join(lines[1:-1])

        return query.strip()


class ShortsListParser(OutputParser):
    """Parser for a list of shorts."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for a list of Short objects."""
        return OutputParser.create_parser(ShortsList)

    @staticmethod
    def parse_output(parser, output: str) -> Optional[List[Short]]:
        """
        Parse the output using the given parser with enhanced error handling.
        """
        try:
            # First try to parse directly
            result = parser.parse(output)
            if isinstance(result, ShortsList):
                return result.shorts
            return result
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                # Look for JSON object with "shorts" key
                json_match = re.search(r'(\{[\s\S]*\})', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Check if it's a ShortsList format
                    if "shorts" in data and isinstance(data["shorts"], list):
                        # Manually create Short objects
                        shorts = []
                        for item in data["shorts"]:
                            try:
                                short = Short(
                                    short_number=item.get("short_number", 1),
                                    title=item.get("title", ""),
                                    narration=item.get("narration", ""),
                                    estimated_duration_seconds=item.get("estimated_duration_seconds", 60),
                                    source_scenes=item.get("source_scenes", [])
                                )
                                shorts.append(short)
                            except Exception as item_e:
                                logger.warning(f"Error creating short: {str(item_e)}")

                        if shorts:
                            return shorts

                # If that fails, look for a JSON array directly
                json_match = re.search(r'(\[[\s\S]*\])', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Manually create Short objects
                    shorts = []
                    for item in data:
                        try:
                            short = Short(
                                short_number=item.get("short_number", 1),
                                title=item.get("title", ""),
                                narration=item.get("narration", ""),
                                estimated_duration_seconds=item.get("estimated_duration_seconds", 60),
                                source_scenes=item.get("source_scenes", [])
                            )
                            shorts.append(short)
                        except Exception as item_e:
                            logger.warning(f"Error creating short: {str(item_e)}")

                    if shorts:
                        return shorts
            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None


class ShortSegmentListParser(OutputParser):
    """Parser for a list of short segments."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for a list of ShortSegment objects."""
        return OutputParser.create_parser(ShortSegmentList)

    @staticmethod
    def parse_output(parser, output: str) -> Optional[List[ShortSegment]]:
        """
        Parse the output using the given parser with enhanced error handling.
        """
        try:
            # First try to parse directly
            result = parser.parse(output)
            if isinstance(result, ShortSegmentList):
                return result.segments
            return result
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                # Look for JSON object with "segments" key
                json_match = re.search(r'(\{[\s\S]*\})', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Check if it's a ShortSegmentList format
                    if "segments" in data and isinstance(data["segments"], list):
                        # Manually create ShortSegment objects
                        segments = []
                        for item in data["segments"]:
                            try:
                                segment = ShortSegment(
                                    short_number=item.get("short_number", 1),
                                    segment_number=item.get("segment_number", 1),
                                    narration=item.get("narration", ""),
                                    estimated_duration_seconds=item.get("estimated_duration_seconds", 6)
                                )
                                segments.append(segment)
                            except Exception as item_e:
                                logger.warning(f"Error creating segment: {str(item_e)}")

                        if segments:
                            return segments

                # If that fails, look for a JSON array directly
                json_match = re.search(r'(\[[\s\S]*\])', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Manually create ShortSegment objects
                    segments = []
                    for item in data:
                        try:
                            segment = ShortSegment(
                                short_number=item.get("short_number", 1),
                                segment_number=item.get("segment_number", 1),
                                narration=item.get("narration", ""),
                                estimated_duration_seconds=item.get("estimated_duration_seconds", 6)
                            )
                            segments.append(segment)
                        except Exception as item_e:
                            logger.warning(f"Error creating segment: {str(item_e)}")

                    if segments:
                        return segments
            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None


class ShortParser(OutputParser):
    """Parser for a single short."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for a Short object."""
        return OutputParser.create_parser(Short)

    @staticmethod
    def parse_output(parser, output: str) -> Optional[Short]:
        """
        Parse the output using the given parser with enhanced error handling.
        """
        try:
            # First try to parse directly
            result = parser.parse(output)
            if isinstance(result, Short):
                return result
            return result
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                # Look for JSON object
                json_match = re.search(r'(\{[\s\S]*\})', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Manually create Short object
                    try:
                        short = Short(
                            short_number=data.get("short_number", 1),
                            title=data.get("title", ""),
                            narration=data.get("narration", ""),
                            estimated_duration_seconds=data.get("estimated_duration_seconds", 60),
                            source_scenes=data.get("source_scenes", [])
                        )
                        return short
                    except Exception as item_e:
                        logger.warning(f"Error creating short: {str(item_e)}")

            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None


class VisualPlanParser(OutputParser):
    """Parser for visual planning data."""

    @staticmethod
    def create() -> PydanticOutputParser:
        """Create a parser for VisualPlan."""
        return OutputParser.create_parser(VisualPlan)

    @staticmethod
    def parse_output(parser, output: str) -> Optional[VisualPlan]:
        """
        Parse the output using the given parser with enhanced error handling.
        """
        try:
            # First try to parse directly
            result = parser.parse(output)
            if isinstance(result, VisualPlan):
                return result
            return result
        except Exception as e:
            logger.warning(f"Direct parsing failed: {str(e)}")

            # Try to extract JSON from the output
            try:
                # Look for JSON object
                json_match = re.search(r'(\{[\s\S]*\})', output)
                if json_match:
                    json_str = json_match.group(1)
                    data = json.loads(json_str)

                    # Manually create VisualPlan object
                    try:
                        # Create VisualSegment objects
                        visual_segments = []
                        segments_data = data.get("segments", [])

                        for segment_data in segments_data:
                            visual_segment = VisualSegment(
                                segment_number=segment_data.get("segment_number", 1),
                                narration=segment_data.get("narration", ""),
                                video_duration=segment_data.get("video_duration", 5),
                                visual_prompt=segment_data.get("visual_prompt", "")
                            )
                            visual_segments.append(visual_segment)

                        visual_plan = VisualPlan(
                            short_number=data.get("short_number", 1),
                            segments=visual_segments
                        )
                        return visual_plan
                    except Exception as item_e:
                        logger.warning(f"Error creating visual plan: {str(item_e)}")

            except Exception as e2:
                logger.warning(f"JSON extraction failed: {str(e2)}")

            # Return None if all parsing attempts fail
            return None