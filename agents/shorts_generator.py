"""
Shorts Generator Agent
---------------------
Extracts individual shorts from the complete story for short-form video generation.
"""

import os
import json
import logging
from typing import List

from crewai import Agent, Task, Crew, Process

from models.schema import Story, Short
from utils.parsers import ShortsListParser

logger = logging.getLogger(__name__)


class ShortsGeneratorAgent:
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the shorts generator agent.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider

        # LLM will be provided via create_rate_limited_agent in the caller when needed
        self.llm = None

    def generate_shorts(self, story: Story) -> List[Short]:
        """
        Generate individual shorts from the complete story.

        Args:
            story (Story): The complete story to extract shorts from

        Returns:
            List[Short]: List of extracted shorts
        """
        logger.info(f"Starting shorts generation from story with {len(story.scenes)} scenes")

        # Create a parser for the list of Short models
        parser = ShortsListParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the shorts generator agent
        generator = Agent(
            role="Shorts Content Strategist",
            goal="Extract compelling short-form video segments from a complete Hindi story that work as standalone content while maintaining narrative flow",
            backstory="""You are an expert in short-form video content creation with deep understanding of
            audience engagement, narrative pacing, and viral content strategies. You specialize in breaking down
            longer stories into bite-sized, compelling segments that can stand alone while maintaining connection
            to the larger narrative. You understand the optimal duration for different types of content and can
            identify natural break points that create hooks and maintain viewer interest.""",
            verbose=self.verbose,
            allow_delegation=False,
            llm=self.llm
        )

        # Convert story to string for the task
        story_str = json.dumps(story.model_dump(), ensure_ascii=False, indent=2)

        # Create the shorts generation task
        generation_task = Task(
            description=f"""
            Analyze the following complete Hindi story and extract individual shorts that can work as 
            standalone short-form videos:

            STORY TITLE: {story.title}

            COMPLETE STORY:
            {story_str}

            Your task is to:

            1. **Identify Natural Break Points**: Find logical segments in the story that can function 
               as complete mini-narratives or compelling story beats.

            2. **Duration Targeting**: Each short should have an estimated audio duration of:
               - Target range: 20 seconds to 1 minute (ideal for maximum engagement)
               - Acceptable range: 20 seconds to 2 minutes
               - Only exceed 1 minute when absolutely necessary to complete a narrative segment
               - Avoid cutting off mid-sentence or mid-thought

            3. **Standalone Functionality**: Each short must:
               - Have a clear beginning, middle, and end (or compelling hook/cliffhanger)
               - Be understandable without requiring knowledge of previous shorts
               - Include enough context for new viewers to follow along
               - End with either resolution or a compelling hook for the next part

            4. **Content Strategy**: Prioritize segments that:
               - Have high emotional impact or dramatic moments
               - Contain surprising revelations or plot twists
               - Feature compelling characters or dialogue
               - Include action sequences or tension-building moments
               - Have strong visual storytelling potential

            5. **Narrative Flow**: Ensure shorts maintain logical sequence and can be watched:
               - As individual standalone pieces
               - As a connected series in order
               - With clear transitions between parts

            6. **Title Generation**: Create engaging titles for each short that:
               - Capture the essence of that segment
               - Create curiosity and encourage viewing
               - Work well for YouTube/social media thumbnails

            IMPORTANT GUIDELINES:
            - Preserve the original Hindi narration text exactly as provided
            - Do not modify, translate, or alter the language mixing
            - Maintain the documentary-style narrative voice
            - Ensure each short tells a complete story beat or creates compelling anticipation
            - Consider the pacing and emotional arc of each segment
            - Think about how each short would perform as individual content

            Extract 3-8 shorts from the story, depending on the content length and natural break points.
            """,
            agent=generator,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[generation_task],
            agents=[generator],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Parse the result using the Pydantic parser
        shorts_list = ShortsListParser.parse_output(parser, result)

        # If parsing fails, log the error and exit
        if shorts_list is None:
            logger.error("Could not parse ShortsGenerator result, Raw output: %s", result)
            raise ValueError("Failed to parse shorts from ShortsGenerator Agent")

        logger.info(f"Successfully generated {len(shorts_list)} shorts from the story")
        
        # Log details about each short
        for short in shorts_list:
            logger.info(f"Short {short.short_number}: '{short.title}' - {short.estimated_duration_seconds}s duration")

        return shorts_list

    def save_shorts_to_directories(self, shorts: List[Short], story_dir: str) -> None:
        """
        Save each short to its own directory within the story directory.

        Args:
            shorts (List[Short]): List of shorts to save
            story_dir (str): Path to the main story directory
        """
        logger.info(f"Saving {len(shorts)} shorts to individual directories")

        for short in shorts:
            # Create directory for this short
            short_dir = os.path.join(story_dir, f"short-{short.short_number}")
            os.makedirs(short_dir, exist_ok=True)

            # Save short data to JSON file
            short_json_path = os.path.join(short_dir, "short.json")
            try:
                with open(short_json_path, 'w', encoding='utf-8') as f:
                    json.dump(short.model_dump(), f, ensure_ascii=False, indent=2)
                
                logger.info(f"Short {short.short_number} saved to {short_json_path}")
                
            except Exception as e:
                logger.error(f"Error saving short {short.short_number} to {short_json_path}: {str(e)}")
                raise

        logger.info("All shorts saved successfully to individual directories")
