"""
Video Generator Agent
--------------------
Generates video content for story segments using Replicate's bytedance/seedance-1-lite model.
Implements a two-phase approach: visual planning followed by video generation.
"""

import os
import logging
import requests
from typing import List, Dict

import replicate

from models.schema import VisualSegment
from agents.visual_planner import VisualPlanningAgent
from utils.shorts_utils import get_shorts_directories

logger = logging.getLogger(__name__)


class VideoGeneratorAgent:
    """
    Agent for generating video content from story segments using Replicate's bytedance/seedance-1-lite model.

    This agent implements a two-phase approach:
    1. Visual Planning: Creates comprehensive visual plans for entire shorts
    2. Video Generation: Generates individual videos using the planned visual prompts
    """

    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai", video_resolution: str = "480p"):
        """
        Initialize the Video Generator Agent.

        Args:
            verbose (bool): Enable verbose output from CrewAI agents
            model (str): Model name to use for LLM
            provider (str): Provider to use for LLM
            video_resolution (str): Video resolution (480p or 720p)
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider
        self.video_resolution = video_resolution

        # Validate Replicate API key
        self.replicate_api_key = os.getenv("REPLICATE_API_KEY")
        if not self.replicate_api_key:
            raise ValueError("Missing required REPLICATE_API_KEY for VideoGeneratorAgent")

        # Configure replicate client
        os.environ['REPLICATE_API_TOKEN'] = self.replicate_api_key

        # Initialize the visual planning agent
        self.visual_planner = VisualPlanningAgent(
            verbose=verbose,
            model=model,
            provider=provider
        )



    def generate_video(self, visual_segment: VisualSegment, output_path: str) -> bool:
        """
        Generate video using Replicate's bytedance/seedance-1-lite model.

        Args:
            visual_segment (VisualSegment): The visual segment containing prompt and duration
            output_path (str): Path to save the generated video

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Prepare input parameters for bytedance/seedance-1-lite
            input_params = {
                "prompt": visual_segment.visual_prompt,
                "duration": visual_segment.video_duration,  # Use the planned duration
                "resolution": self.video_resolution,
                "aspect_ratio": "9:16"  # Vertical format for shorts
            }

            logger.info(f"Generating video for segment {visual_segment.segment_number}")
            logger.debug(f"Input params: {input_params}")

            # Generate video using Replicate
            video_url = replicate.run(
                "bytedance/seedance-1-lite",
                input=input_params
            )

            logger.debug(f"Video generated, downloading from: {video_url}")

            # Download the video file
            response = requests.get(video_url, stream=True, timeout=300)  # 5 minute timeout
            response.raise_for_status()

            # Save to file
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            logger.info(f"Video saved to: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error generating video for segment {visual_segment.segment_number}: {str(e)}")
            return False

    def process_short_segments(self, short_dir: str) -> List[str]:
        """
        Process all segments in a short directory to generate videos using two-phase approach.

        Phase 1: Visual Planning (if not already done)
        Phase 2: Video Generation

        Args:
            short_dir (str): Path to the short directory

        Returns:
            List[str]: List of generated video file paths
        """
        short_name = os.path.basename(short_dir)

        # Phase 1: Visual Planning
        visual_plan = self.visual_planner.load_visual_plan(short_dir)

        if visual_plan is None:
            logger.info(f"Creating visual plan for {short_name}")

            if not self.visual_planner.process_short(short_dir):
                logger.error(f"Failed to create visual plan for {short_name}")
                return []

            visual_plan = self.visual_planner.load_visual_plan(short_dir)

            if visual_plan is None:
                logger.error(f"Could not load visual plan after creation for {short_name}")
                return []

        else:
            logger.info(f"Using existing visual plan for {short_name}")

        # Phase 2: Video Generation
        # Create video directory if it doesn't exist
        video_dir = os.path.join(short_dir, 'video')
        os.makedirs(video_dir, exist_ok=True)

        generated_videos = []

        for visual_segment in visual_plan.segments:
            # Generate filename matching the audio file pattern
            clean_short_name = short_name.replace("-", "_")
            video_filename = f"{clean_short_name}_segment_{visual_segment.segment_number}.mp4"
            video_path = os.path.join(video_dir, video_filename)

            # Skip if video already exists (for --continue functionality)
            if os.path.exists(video_path):
                logger.info(f"Video already exists, skipping: {video_path}")
                generated_videos.append(video_path)
                continue

            # Generate video using the planned visual prompt
            if self.generate_video(visual_segment, video_path):
                generated_videos.append(video_path)
            else:
                logger.warning(f"Failed to generate video for segment {visual_segment.segment_number}")

        logger.info(f"Generated {len(generated_videos)} videos for {short_name}")
        return generated_videos

    def process_all_shorts(self, story_dir: str) -> Dict[str, List[str]]:
        """
        Process all shorts in the story directory to generate videos.

        Args:
            story_dir (str): Path to the story directory

        Returns:
            Dict[str, List[str]]: Dictionary mapping short names to lists of generated video paths
        """
        short_dirs = get_shorts_directories(story_dir)
        if not short_dirs:
            logger.warning(f"No shorts directories found in {story_dir}")
            return {}

        all_generated_videos = {}

        for short_dir in short_dirs:
            short_name = os.path.basename(short_dir)
            logger.info(f"Processing videos for {short_name}")

            try:
                generated_videos = self.process_short_segments(short_dir)
                all_generated_videos[short_name] = generated_videos
                
            except Exception as e:
                logger.error(f"Error processing {short_name}: {str(e)}")
                all_generated_videos[short_name] = []

        total_videos = sum(len(videos) for videos in all_generated_videos.values())
        logger.info(f"Video generation completed. Generated {total_videos} videos across {len(short_dirs)} shorts")

        return all_generated_videos
