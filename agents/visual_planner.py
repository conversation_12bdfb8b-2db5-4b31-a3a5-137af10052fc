"""
Visual Planning Agent
--------------------
Generates comprehensive visual plans for entire short segments to improve context and consistency.
"""

import os
import json
import logging
from typing import List, Optional

from crewai import Task, Crew, Process

from models.schema import Short, ShortSegment, VisualPlan
from utils.agent_factory import create_rate_limited_agent
from utils.shorts_utils import get_shorts_directories, load_short_from_directory, load_segments_from_directory
from utils.parsers import VisualPlanParser

logger = logging.getLogger(__name__)


class VisualPlanningAgent:
    """
    Agent for generating comprehensive visual plans for entire short segments.
    
    This agent analyzes complete short segments to create cohesive visual prompts
    with better story context and narrative consistency across all segments.
    """

    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the Visual Planning Agent.

        Args:
            verbose (bool): Whether to enable verbose logging
            model (str): The model to use for the agent
            provider (str): The provider to use for the agent
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider

    def create_visual_plan(self, short: Short, segments: List[ShortSegment]) -> VisualPlan:
        """
        Create a comprehensive visual plan for all segments in a short.

        Args:
            short (Short): The short containing the complete context
            segments (List[ShortSegment]): List of segments to create visual plans for

        Returns:
            VisualPlan: Complete visual planning data for the short
        """
        logger.info(f"Creating visual plan for short {short.short_number} with {len(segments)} segments")

        # Create a parser for the VisualPlan model
        parser = VisualPlanParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the visual planning agent
        visual_planning_agent = create_rate_limited_agent(
            role="Visual Story Planner",
            goal="Create comprehensive visual plans for Hindi story segments that ensure narrative consistency and optimal video generation with bytedance/seedance-1-lite",
            backstory="""You are an expert visual storyteller and AI video generation specialist with deep understanding of 
            the bytedance/seedance-1-lite model capabilities. You excel at analyzing complete story contexts to create 
            cohesive visual narratives that maintain consistency across multiple video segments. You understand how to 
            create visual prompts that work perfectly with text-to-video AI models while ensuring each segment flows 
            naturally into the next, creating a compelling visual story experience.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose
        )

        # Prepare segment information for the task
        segments_info = []
        for segment in segments:
            segments_info.append(f"""
Segment {segment.segment_number}:
- Narration: {segment.narration}
- Duration: {segment.estimated_duration_seconds} seconds
""")

        segments_text = "\n".join(segments_info)

        # Create the visual planning task
        planning_task = Task(
            description=f"""
            Create a comprehensive visual plan for the following Hindi short story that will be used with 
            the bytedance/seedance-1-lite video generation model:

            SHORT CONTEXT:
            Title: {short.title}
            Total Duration: {short.estimated_duration_seconds} seconds
            Complete Narration: {short.narration}

            SEGMENTS TO PLAN:
            {segments_text}

            Your task is to:

            1. **Analyze Complete Context**: Understand the full story arc, key themes, and narrative flow across all segments.

            2. **Create Cohesive Visual Narrative**: Design visual scenes that work together to tell a compelling story, 
               ensuring smooth transitions and consistent visual elements across segments.

            3. **Optimize for bytedance/seedance-1-lite**: Create visual prompts that work perfectly with this specific 
               text-to-video model, considering its strengths in:
               - Realistic human expressions and movements
               - Dynamic camera movements and angles
               - Professional lighting and composition
               - Vertical video format (9:16 aspect ratio)

            4. **Set Appropriate Video Durations**: For each segment, choose between 5 or 10 seconds based on:
               - 5 seconds: Quick, punchy statements, dramatic reveals, or simple actions
               - 10 seconds: Complex explanations, emotional moments, or scenes requiring more development

            5. **Ensure Visual Consistency**: Maintain consistent visual style, lighting, and atmosphere across 
               all segments while allowing for natural story progression.

            6. **Create Engaging Visuals**: Design scenes that are visually compelling and complement the Hindi 
               narration, enhancing the storytelling experience.

            For each segment, provide:
            - segment_number: The segment number
            - narration: The exact narration text
            - video_duration: Either 5 or 10 seconds based on content needs
            - visual_prompt: A detailed, engaging visual description (2-3 sentences) optimized for bytedance/seedance-1-lite

            {format_instructions}
            """,
            agent=visual_planning_agent,
            expected_output=format_instructions
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[planning_task],
            agents=[visual_planning_agent],
            verbose=self.verbose,
        )

        try:
            crew_output = crew.kickoff()
            result = crew_output.raw

            # Parse the result using the Pydantic parser
            visual_plan = VisualPlanParser.parse_output(parser, result)

            if visual_plan is None:
                logger.error(f"Could not parse visual planning result for short {short.short_number}")
                logger.error(f"Raw output: {result}")
                raise ValueError(f"Failed to parse visual plan from Visual Planning Agent for short {short.short_number}")

            # Validate that we have the correct number of segments
            if len(visual_plan.segments) != len(segments):
                logger.warning(f"Visual plan has {len(visual_plan.segments)} segments, expected {len(segments)}")

            # Validate video durations
            valid_durations = {5, 10}
            for visual_segment in visual_plan.segments:
                if visual_segment.video_duration not in valid_durations:
                    logger.warning(f"Segment {visual_segment.segment_number} has invalid duration {visual_segment.video_duration}s, adjusting to 5s")
                    visual_segment.video_duration = 5

            logger.info(f"Visual plan created successfully for short {short.short_number}")
            logger.info(f"  - Planned {len(visual_plan.segments)} segments")
            
            # Log duration breakdown
            duration_counts = {5: 0, 10: 0}
            for visual_segment in visual_plan.segments:
                duration_counts[visual_segment.video_duration] += 1
            
            logger.info(f"  - Duration breakdown: {duration_counts[5]}x5s, {duration_counts[10]}x10s")

            return visual_plan

        except Exception as e:
            logger.error(f"Error creating visual plan for short {short.short_number}: {str(e)}")
            raise

    def save_visual_plan(self, visual_plan: VisualPlan, short_dir: str) -> None:
        """
        Save visual plan to JSON file in the short directory.

        Args:
            visual_plan (VisualPlan): The visual plan to save
            short_dir (str): Path to the short directory
        """
        visuals_json_path = os.path.join(short_dir, "visuals.json")
        
        try:
            with open(visuals_json_path, 'w', encoding='utf-8') as f:
                json.dump(visual_plan.model_dump(), f, ensure_ascii=False, indent=2)
            
            logger.info(f"Visual plan saved to: {visuals_json_path}")
            
        except Exception as e:
            logger.error(f"Error saving visual plan to {visuals_json_path}: {str(e)}")
            raise

    def load_visual_plan(self, short_dir: str) -> Optional[VisualPlan]:
        """
        Load visual plan from JSON file in the short directory.

        Args:
            short_dir (str): Path to the short directory

        Returns:
            Optional[VisualPlan]: The loaded visual plan, or None if not found/invalid
        """
        visuals_json_path = os.path.join(short_dir, "visuals.json")
        
        if not os.path.exists(visuals_json_path):
            return None
        
        try:
            with open(visuals_json_path, 'r', encoding='utf-8') as f:
                visual_plan_data = json.load(f)
                return VisualPlan(**visual_plan_data)
        except Exception as e:
            logger.error(f"Error loading visual plan from {visuals_json_path}: {str(e)}")
            return None

    def process_short(self, short_dir: str) -> bool:
        """
        Process a single short directory to create visual plan.

        Args:
            short_dir (str): Path to the short directory

        Returns:
            bool: True if successful, False otherwise
        """
        short_name = os.path.basename(short_dir)
        
        # Check if visual plan already exists
        if self.load_visual_plan(short_dir) is not None:
            logger.info(f"Visual plan already exists for {short_name}, skipping")
            return True

        try:
            # Load the short data
            short = load_short_from_directory(short_dir)
            if not short:
                logger.error(f"Could not load short data from {short_dir}")
                return False

            # Load the segments data
            segments = load_segments_from_directory(short_dir)
            if not segments:
                logger.error(f"Could not load segments data from {short_dir}")
                return False

            # Create visual plan
            visual_plan = self.create_visual_plan(short, segments)

            # Save visual plan
            self.save_visual_plan(visual_plan, short_dir)

            logger.info(f"Successfully created visual plan for {short_name}")
            return True

        except Exception as e:
            logger.error(f"Error processing visual plan for {short_name}: {str(e)}")
            return False

    def process_all_shorts(self, story_dir: str) -> bool:
        """
        Process all shorts in the story directory to create visual plans.

        Args:
            story_dir (str): Path to the story directory

        Returns:
            bool: True if all shorts processed successfully, False otherwise
        """
        short_dirs = get_shorts_directories(story_dir)
        if not short_dirs:
            logger.warning(f"No shorts directories found in {story_dir}")
            return False

        success_count = 0
        total_shorts = len(short_dirs)

        for short_dir in short_dirs:
            if self.process_short(short_dir):
                success_count += 1

        logger.info(f"Visual planning completed: {success_count}/{total_shorts} shorts processed successfully")
        return success_count == total_shorts
