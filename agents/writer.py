"""
Writer Agent
-----------
Creates Hindi documentary-style narration from raw research reports.
"""

import json
import logging

from crewai import Task, Crew, Process

from utils.agent_factory import create_rate_limited_agent
from utils.parsers import StoryParser
from models.schema import Story, PerplexityReport

logger = logging.getLogger(__name__)

class WriterAgent:
    """
    Agent for creating Hindi documentary-style narration from raw research reports.

    This agent takes raw research reports from Perplexity AI and creates compelling Hindi documentary
    narrations inspired by popular Hindi documentary creators like <PERSON><PERSON><PERSON>.
    """

    def __init__(self,
                 verbose: bool = False,
                 model: str = "gpt-4o-mini",
                 provider: str = "openai") -> None:
        """
        Initialize the Writer Agent.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI. Defaults to False.
            model (str): The model to use for the agent. Defaults to "gpt-4o-mini".
            provider (str): The LLM provider to use. Defaults to "openai".
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider

        logger.info(f"WriterAgent initialized with model: {model}, provider: {provider}")

    def write_documentary_story(self,
                                perplexity_report: PerplexityReport,
                                title: str) -> Story:
        """
        Create a Hindi documentary-style narration from a raw research report.

        Args:
            perplexity_report (PerplexityReport): The raw research report from Perplexity AI
            title (str): The title of the story

        Returns:
            Story: Story data in structured format using Pydantic model
        """
        logger.info(f"Creating documentary narration for title: '{title}'")

        # Create a parser for the Story model
        parser = StoryParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the documentary writer agent
        writer = create_rate_limited_agent(
            role="Hindi Documentary Narrator and Storyteller",
            goal="Create compelling Hindi documentary-style narrations from raw research reports, inspired by popular Hindi documentary creators like Nitish Rajput",
            backstory="""You are an expert Hindi documentary narrator and storyteller with extensive experience in
            creating engaging, educational content for Indian audiences. Your narration style is inspired by popular
            Hindi documentary creators like Nitish Rajput, known for their compelling storytelling and factual accuracy.

            Your expertise includes:
            - Creating engaging Hindi narrations that present complex information in accessible ways
            - Structuring documentary content with strong narrative arcs and emotional engagement
            - Using rhetorical questions and storytelling techniques for maximum impact
            - Balancing factual accuracy with compelling storytelling
            - Understanding Indian cultural context and social dynamics
            - Creating content that educates while entertaining
            - Using appropriate Hindi vocabulary and expressions for documentary narration
            - Building suspense and maintaining audience engagement throughout the narrative

            Your narration style characteristics:
            - Clear, authoritative voice that builds trust with the audience
            - Rhetorical questions that engage the audience and create curiosity
            - Smooth transitions between different aspects of the story
            - Cultural references and context that resonate with Indian audiences
            - Factual presentation with emotional storytelling elements
            - Strong opening hooks and memorable closing statements

            You specialize in creating documentary narrations that are:
            - Factually accurate and well-researched
            - Emotionally engaging and thought-provoking
            - Culturally sensitive and contextually appropriate
            - Educational yet entertaining
            - Structured for optimal audience retention
            - Suitable for Hindi-speaking audiences across different demographics

            Your goal is to transform raw research reports into compelling Hindi documentary narrations
            that inform, engage, and inspire audiences while maintaining the highest standards of factual accuracy.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False
        )

        # Extract report content and citations from Perplexity report
        report_content = perplexity_report.report_content
        citations = perplexity_report.citations

        # Create the documentary writing task
        task_description = f"""
        Create a compelling Hindi documentary-style narration based on the following raw research report.
        Your narration should be inspired by popular Hindi documentary creators like Nitish Rajput, focusing on
        factual accuracy while maintaining high audience engagement.

        RAW RESEARCH REPORT:
        {report_content}

        CITATIONS AND SOURCES:
        {json.dumps(citations, indent=2)}

        TITLE: {title}

        Create a documentary narration that includes:

        **NARRATIVE STRUCTURE REQUIREMENTS:**
        1. **Opening Hook** (Scene 1): Start with a powerful, attention-grabbing statement or question that immediately
           draws the audience into the story. Use dramatic tension or an intriguing fact.

        2. **Background Setup**: Provide essential context and background information that helps the
           audience understand the significance of the incident/event.

        3. **Main Narrative**: Present the core events in a logical, chronological flow with:
           - Key events and timeline
           - Main participants and their roles
           - Multiple perspectives where relevant
           - Cultural and social context

        4. **Analysis and Impact**: Discuss the consequences, impact, and broader implications of the incident.

        5. **Conclusion**: End with lessons learned, current status, or thought-provoking reflections.

        **NARRATION STYLE REQUIREMENTS:**
        - Use modern conversational Hindi that naturally incorporates commonly-used English terms
        - Use English words for technical terms, modern concepts, sports, technology, and contemporary terms
        - Use English terminology where it's more commonly used in everyday conversation than pure Hindi
        - Maintain Hindi as the primary language while mixing English naturally as modern Hindi speakers do
        - Use a conversational yet authoritative tone
        - Include rhetorical questions to engage the audience
        - Incorporate cultural references and context relevant to Indian audiences
        - Balance factual information with emotional storytelling
        - Use transitions that maintain narrative flow and audience interest
        - Write only in Hindi (Devanagari script) and English - avoid any other languages unless absolutely necessary or relevant to the story
        - Do not include stage directions or speech annotations like [pause], (pause), [whispers], [giggles], [dramatic pause]. The narration must be clean text only.

        **ADVANCED STORYTELLING TECHNIQUES:**
        - **Audience Connection**: Write as if you're talking to a close friend, not lecturing to a crowd. Visualize a specific person you're telling this story to.
        - **Engagement Over Lecturing**: Talk TO your audience, not AT them. Use rhetorical questions and create moments for mental participation.
        - **Context and Conflict Dance**: Create a dynamic flow between providing context and introducing conflicts. Use "लेकिन" (but) and "इसलिए" (therefore) instead of "और फिर" (and then) to create compelling story beats.
        - **Rhythm and Pacing**: Vary sentence lengths naturally - mix short, punchy statements with medium explanatory sentences and longer descriptive passages. This creates a pleasant, subconscious rhythm.
        - **Conversational Tone**: Break down barriers between narrator and listener. Make the audience feel like they're part of an intimate conversation rather than a formal presentation.
        - **Strategic Direction**: Start with the end in mind. Craft memorable opening and closing lines that connect to each other, creating narrative loops and strong story structure.
        - **Unique Story Lens**: Find a distinctive angle or perspective on the topic that differentiates your narration from others covering similar subjects.
        - **Powerful Hooks**: Create punchy, plot-indicative opening lines that immediately grab attention. Avoid vague or generic openings.

        **TECHNICAL REQUIREMENTS:**
        - Create at least 12 scenes. Create more if needed.
        - Each scene should be 30-60 seconds of narration
        - Do not include visual descriptions, transitions, or narrative-purpose fields in the output; only the Hindi narration per scene will be produced
        - Create smooth transitions between scenes implicitly through the narration
        - Ensure factual accuracy based on the research report

        **CONTENT GUIDELINES:**
        - Maintain journalistic integrity and factual accuracy
        - Present multiple perspectives where they exist in the research
        - Use appropriate Hindi vocabulary for documentary narration
        - Include cultural sensitivity and context
        - Create educational value while maintaining entertainment
        - End with meaningful insights or lessons learned

        The final output should be a structured story with scenes that can be easily converted to audio narration."""

        # Create the documentary writing task
        writing_task = Task(
            description=task_description,
            agent=writer,
            expected_output=format_instructions
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[writing_task],
            agents=[writer],
            verbose=self.verbose
        )

        try:
            crew_output = crew.kickoff()
            result = crew_output.raw

            # Parse the result using the Pydantic parser
            story = StoryParser.parse_output(parser, result)

            # If parsing fails, raise an error
            if story is None:
                logger.error("Could not parse Writer result, Raw output: %s", result)
                raise ValueError("Failed to parse story from Writer Agent")

            logger.info(f"Documentary story creation completed successfully with {len(story.scenes)} scenes")
            return story

        except Exception as e:
            logger.error(f"Error creating documentary story: {str(e)}")
            raise
