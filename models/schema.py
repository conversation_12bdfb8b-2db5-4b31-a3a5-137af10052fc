"""
Schema Models
------------
Pydantic models for structured data in the story generation pipeline.
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Any


class Setting(BaseModel):
    """Setting description for a story location."""
    location: str = Field(description="Name or description of the location")
    description: str = Field(description="Detailed description of the location")
    atmosphere: str = Field(description="Atmosphere or mood of the location")


class ResearchData(BaseModel):
    """Research data structure for both real and fictional stories."""
    background_information: str = Field(description="Background information about the topic or story setting")
    key_events: List[str] = Field(default_factory=list, description="List of key events or plot points")
    cultural_context: str = Field(default="", description="Cultural context relevant to the story")
    potential_story_elements: List[str] = Field(default_factory=list, description="Potential elements to include in the story")
    possible_plot_twists: List[str] = Field(default_factory=list, description="Possible plot twists or unexpected developments")
    settings: List[Setting] = Field(default_factory=list, description="Settings or locations for the story")


class EnhancedResearchData(ResearchData):
    """Enhanced research data structure with additional narrative elements."""
    narrative_structure: str = Field(default="", description="Refined narrative structure and flow for the story")
    thematic_elements: List[str] = Field(default_factory=list, description="Key thematic elements and motifs in the story")
    emotional_arcs: List[str] = Field(default_factory=list, description="Emotional arcs and character development paths")
    dialogue_suggestions: List[str] = Field(default_factory=list, description="Suggested dialogue styles and key exchanges")
    cultural_authenticity_notes: str = Field(default="", description="Notes on ensuring cultural authenticity in the story")


class Scene(BaseModel):
    """A scene in the story."""
    scene_number: int = Field(
        description="Sequential number of the scene"
    )
    narration: str = Field(
        description="Hindi narration text for this scene (MUST be in Devanagari script)"
    )


class Story(BaseModel):
    """Complete story structure with scenes."""
    title: str = Field(
        description="Title of the story (can be in Hindi or English, depending on user input)"
    )
    scenes: List[Scene] = Field(
        description="List of scenes in the story"
    )


class PerplexityReport(BaseModel):
    """Raw report data from Perplexity AI Sonar."""
    query: str = Field(
        description="The research query that was sent to Perplexity AI"
    )
    report_content: str = Field(
        description="Extracted report content (after </think> block)"
    )
    citations: List[str] = Field(
        default_factory=list,
        description="List of citation URLs from the research"
    )
    usage_stats: Dict[str, Any] = Field(
        default_factory=dict,
        description="API usage statistics from Perplexity"
    )


class TrendingKeyword(BaseModel):
    """Trending keyword data from DataForSEO."""
    keyword: str = Field(
        description="The trending keyword"
    )
    average_trend: float = Field(
        description="Average trend value over the analyzed period"
    )
    max_trend: int = Field(
        description="Maximum trend value recorded"
    )
    data_points: int = Field(
        description="Number of data points used for calculation"
    )


class Short(BaseModel):
    """A short video segment extracted from the complete story."""
    short_number: int = Field(
        description="Sequential number of the short (1, 2, 3, etc.)"
    )
    title: str = Field(
        description="Title of this short segment"
    )
    narration: str = Field(
        description="Complete Hindi narration text for this short (MUST be in Devanagari script)"
    )
    estimated_duration_seconds: int = Field(
        description="Estimated total duration of this short in seconds (20-120 seconds target)"
    )
    source_scenes: List[int] = Field(
        description="List of original scene numbers that this short was extracted from"
    )


class ShortSegment(BaseModel):
    """A segment of a short for text-to-video generation."""
    short_number: int = Field(
        description="Short number this segment belongs to"
    )
    segment_number: int = Field(
        description="Sequential number of this segment within the short"
    )
    narration: str = Field(
        description="Hindi narration text for this segment (MUST be in Devanagari script)"
    )
    estimated_duration_seconds: int = Field(
        description="Estimated duration of this segment in seconds (5, 6, or 10 seconds for text-to-video)",
        default=6
    )


class ShortsList(BaseModel):
    """A list of shorts extracted from a story."""
    shorts: List[Short] = Field(
        description="List of shorts extracted from the story"
    )


class VisualSegment(BaseModel):
    """A visual segment for video generation with planning data."""
    segment_number: int = Field(
        description="Sequential number of this segment within the short"
    )
    narration: str = Field(
        description="Hindi narration text for this segment (MUST be in Devanagari script)"
    )
    video_duration: int = Field(
        description="Video duration in seconds for Replicate API (can only be 5 or 10 seconds)",
        ge=5,
        le=10
    )
    visual_prompt: str = Field(
        description="Detailed visual description for video generation optimized for bytedance/seedance-1-lite"
    )


class VisualPlan(BaseModel):
    """Visual planning data for a complete short segment."""
    short_number: int = Field(
        description="Short number this visual plan belongs to"
    )
    segments: List[VisualSegment] = Field(
        description="List of visual segments with planning data"
    )


class ShortSegmentList(BaseModel):
    """A list of short segments for a specific short."""
    segments: List[ShortSegment] = Field(
        description="List of segments for a short"
    )


class ShortsMetadata(BaseModel):
    """Metadata for a short video."""
    short_number: int = Field(
        description="Sequential number of the short"
    )
    title: str = Field(
        description="Title of the short in English"
    )
    description: str = Field(
        description="Description of the short in English"
    )
    keywords: List[str] = Field(
        description="List of keywords/tags for the short"
    )
    estimated_duration: str = Field(
        description="Estimated duration of the short (e.g., '45 seconds')"
    )


class GlobalMetadata(BaseModel):
    """Global metadata for the complete story that applies to all shorts."""
    base_title: str = Field(
        description="Base title that will be used across all shorts with part numbering"
    )
    base_hashtags: List[str] = Field(
        description="Base hashtags that are relevant to the overall story theme (15-20 hashtags)"
    )
    trending_keywords_used: List[str] = Field(
        description="List of trending keywords incorporated in the base metadata"
    )
    target_audience: str = Field(
        description="Description of the target audience for the story"
    )
    content_category: str = Field(
        description="Content category (e.g., Documentary, News, Educational)"
    )
    total_shorts: int = Field(
        description="Total number of shorts in this story"
    )


class ShortSpecificMetadata(BaseModel):
    """Short-specific metadata that supplements the global metadata."""
    short_number: int = Field(
        description="Sequential number of the short"
    )
    additional_hashtags: List[str] = Field(
        description="Additional hashtags specific to this short's content (5-10 hashtags)"
    )
    custom_description: str = Field(
        description="Custom description for this short using pre-generated keywords"
    )
    estimated_duration: str = Field(
        description="Estimated duration of the short (e.g., '45 seconds')"
    )
